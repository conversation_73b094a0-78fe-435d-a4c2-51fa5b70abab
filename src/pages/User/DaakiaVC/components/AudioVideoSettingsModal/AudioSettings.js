import React, {
  useState,
} from "react";
import "./AudioVideoSettingsModal.scss";
import { MediaDeviceMenu } from "@livekit/components-react";
import { FaAngleRight } from "react-icons/fa";
import { ReactComponent as MicIcon } from "./Assets/microphoneDevice.svg";
import { ReactComponent as NoiseCancellationIcon } from "./Assets/noiseCancellation.svg";
import { ReactComponent as EchoCancellationIcon } from "./Assets/echoCancellation.svg";
import Switch from "../../../../../components/Antd/Switch/index.ant";

export default function AudioSettings({
  deviceId,
  setDeviceId,
  track,
  room,
}) {
  const [micDeviceName, setMicDeviceName] = useState("Unknown Device");
  const [noiseCancellation, setNoiseCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression
  );
  const [echoCancellation, setEchoCancellation] = React.useState(
    room?.options?.audioCaptureDefaults?.echoCancellation
  );


  const handleNoiseCancellation = () => {
    setNoiseCancellation((prevNoiseCancellation) => {
      const newNoiseCancellation = !prevNoiseCancellation;
      // Update room option for noise suppression
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.noiseSuppression =
          newNoiseCancellation;
      }
      return newNoiseCancellation;
    });
  };

  const handleEchoCancellation = () => {
    setEchoCancellation((prevEchoCancellation) => {
      const newEchoCancellation = !prevEchoCancellation;
      // Update room option for echo cancellation
      if (room?.options?.audioCaptureDefaults) {
        room.options.audioCaptureDefaults.echoCancellation =
          newEchoCancellation;
      }
      return newEchoCancellation;
    });
  };

  return (
    <div className="audio-video-settings-modal-options">
      <h4>Audio Settings</h4>

      {/* Mic Device Selection */}
      <div className="audio-video-settings-modal-option">
        <MicIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-option-dropdown device-options-mic">
          <MediaDeviceMenu
            initialSelection={deviceId}
            kind="audioinput"
            // disabled={!track}
            tracks={{ audioinput: track }}
            onActiveDeviceChange={async (_, id) => {
              setDeviceId(id);

              // Fetch all media devices
              const devices = await navigator.mediaDevices.enumerateDevices();

              // Find the device with the matching id and set its label as the name
              const selectedDevice = devices.find(
                (device) =>
                  device.deviceId === id && device.kind === "audioinput"
              );
              if (selectedDevice) {
                setMicDeviceName(selectedDevice.label);
              }
            }}
            className="audio-video-settings-modal-device-options"
          >
            {/* Mic Device Name */}
            {micDeviceName.length > 30
              ? `${micDeviceName.slice(0, 30)}...`
              : micDeviceName}
            <FaAngleRight />
          </MediaDeviceMenu>
        </div>
      </div>
      {/* Noise Cancellation */}
      <div className="audio-video-settings-modal-option">
        <NoiseCancellationIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-option-switch">
          <span className="device-name">Noise Cancellation</span>
          <Switch
            className="audio-video-settings-modal-switch"
            checked={noiseCancellation}
            onChange={handleNoiseCancellation}
          />
        </div>
      </div>

      {/* Echo Cancellation */}
      <div className="audio-video-settings-modal-option">
        <EchoCancellationIcon className="audio-video-settings-modal-icon" />
        <div className="audio-video-settings-modal-option-switch">
          <span className="device-name">Echo Cancellation</span>
          <Switch
            className="audio-video-settings-modal-switch"
            checked={echoCancellation}
            onChange={handleEchoCancellation}
          />
        </div>
      </div>
    </div>
  );
}
