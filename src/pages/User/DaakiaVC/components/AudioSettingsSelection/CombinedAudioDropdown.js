import React, { useEffect, useRef, useState, useCallback } from "react";
import { Dropdown, Slider } from "antd";
import { useMediaDeviceSelect } from "@livekit/components-react";
import AudioDeviceDropdown from "./AudioDeviceDropdown";
import SpeakerDeviceDropdown from "./SpeakerDeviceDropdown";
import { ReactComponent as SpeakerIcon } from "../AudioVideoSettingsModal/Assets/speaker.svg";
import { ReactComponent as SpeakerFlowIcon } from "../AudioVideoSettingsModal/Assets/speakerFlow.svg";
import "./CombinedAudioDropdown.scss";

const CombinedAudioDropdown = React.memo(function CombinedAudioDropdown({
  micSelection = undefined,
  speakerSelection = undefined,
  onMicChange = undefined,
  onSpeakerChange = undefined,
  className = "",
  // Volume props for local speaker output control
  speakerVolume = 100,
  onSpeakerVolumeChange = undefined
}) {

  // Local volume state (0-100)
  const [localVolume, setLocalVolume] = useState(speakerVolume);

  // Get LiveKit device management hooks
  const { devices: audioInputDevices, activeDeviceId: activeMicId, setActiveMediaDevice: setActiveMicDevice } = useMediaDeviceSelect({ kind: "audioinput" });
  const { devices: audioOutputDevices, activeDeviceId: activeSpeakerId, setActiveMediaDevice: setActiveSpeakerDevice } = useMediaDeviceSelect({ kind: "audiooutput" });

  // Track if we've set initial devices (separate for each device type)
  const hasSetInitialMic = useRef(false);
  const hasSetInitialSpeaker = useRef(false);

  // Volume control function using HTMLAudioElement.volume property
  // This controls the local output volume (your speaker volume)
  const applyVolumeToLocalOutput = useCallback((volume) => {
    // Convert volume from 0-100 to 0-1.0 for HTMLAudioElement
    const normalizedVolume = volume / 100;

    // Apply volume to all audio and video elements (local output control)
    const audioElements = document.querySelectorAll('audio');
    audioElements.forEach(audio => {
      audio.volume = normalizedVolume;
    });

    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      video.volume = normalizedVolume;
    });

    console.log(`Applied local output volume: ${volume}% (${normalizedVolume}) to ${audioElements.length} audio and ${videoElements.length} video elements`);
  }, []);

  // Handle volume change
  const handleVolumeChange = useCallback((value) => {
    setLocalVolume(value);
    applyVolumeToLocalOutput(value);

    // Call parent callback if provided
    if (onSpeakerVolumeChange) {
      onSpeakerVolumeChange(value);
    }
  }, [applyVolumeToLocalOutput, onSpeakerVolumeChange]);

  // Update local volume when prop changes
  useEffect(() => {
    setLocalVolume(speakerVolume);
  }, [speakerVolume]);

  // Apply volume to existing elements when component mounts or volume changes
  useEffect(() => {
    applyVolumeToLocalOutput(localVolume);
  }, [localVolume, applyVolumeToLocalOutput]);

  // Apply initial microphone device (ONLY ONCE for initial setup)
  useEffect(() => {
    if (hasSetInitialMic.current) return; // Only run once

    if (micSelection && micSelection !== activeMicId && audioInputDevices.length > 0) {
      const deviceExists = audioInputDevices.some(device => device.deviceId === micSelection);
      if (deviceExists) {
        setActiveMicDevice(micSelection).catch(error => {
          console.error('Failed to set initial mic device:', error);
        });
        hasSetInitialMic.current = true;
      }
    }
  }, [micSelection, activeMicId, setActiveMicDevice, audioInputDevices]);

  // Apply initial speaker device (ONLY ONCE for initial setup)
  useEffect(() => {
    if (hasSetInitialSpeaker.current) return; // Only run once

    if (speakerSelection && speakerSelection !== activeSpeakerId && audioOutputDevices.length > 0) {
      const deviceExists = audioOutputDevices.some(device => device.deviceId === speakerSelection);
      if (deviceExists) {
        setActiveSpeakerDevice(speakerSelection).catch(error => {
          console.error('Failed to set initial speaker device:', error);
        });
        hasSetInitialSpeaker.current = true;
      }
    }
  }, [speakerSelection, activeSpeakerId, setActiveSpeakerDevice, audioOutputDevices]);
  const dropdownContent = (
    <div className="combined-audio-dropdown-menu">
      <div className="audio-settings-row">
        {/* Microphone Section */}
        <div className="audio-setting-section">
          <AudioDeviceDropdown
            kind="audioinput"
            onActiveDeviceChange={onMicChange}
            initialSelection={micSelection}
            className="device-dropdown"
          />
        </div>
        <div className="audio-setting-section">
          <SpeakerDeviceDropdown
            kind="audiooutput"
            onActiveDeviceChange={onSpeakerChange}
            initialSelection={speakerSelection}
            className="device-dropdown"
          />
        </div>
      </div>

      {/* Speaker Volume Control Section */}
      <div className="volume-control-section">
        <div className="volume-control-label">
          <span>Speaker Volume</span>
          <small>Adjust your local speaker output volume</small>
        </div>
        <div className="volume-control-slider">
          <SpeakerIcon className="volume-icon" />
          <Slider
            value={localVolume}
            onChange={handleVolumeChange}
            min={0}
            max={100}
            tooltip={{ formatter: (val) => `${val}%` }}
            className="volume-slider"
          />
          <SpeakerFlowIcon className="volume-icon" />
          <span className="volume-percentage">{localVolume}%</span>
        </div>
      </div>

      {/* Noise Suppression Section - COMMENTED OUT - Use Settings Modal instead */}
      {/* <div className="noise-suppression-section">
        <div className="noise-suppression-label">
          <span>Noise Suppression</span>
          <small>Reduce background noise during calls</small>
        </div>
        <Switch
          checked={isNoiseSuppressionEnabled}
          onChange={(enabled) => {is 
            // If Shiguredo is being turned ON, turn OFF LiveKit noise suppression
            if (enabled && room?.options?.audioCaptureDefaults?.noiseSuppression) {
              room.options.audioCaptureDefaults.noiseSuppression = false;
            }
            setIsNoiseSuppressionEnabled(enabled);
          }}
          size="default"
        />
      </div> */}
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="combined-audio-dropdown-button"
        type="button"
        aria-label="Audio settings - microphone and speaker selection"
        aria-haspopup="listbox"
        aria-expanded="false"
      >
        {/* Removed Unicode arrow - now using CSS-based arrow via ::after pseudo-element */}
      </button>
    </Dropdown>
  );
});





export default CombinedAudioDropdown;
