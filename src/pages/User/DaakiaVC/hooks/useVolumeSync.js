import { useEffect, useMemo } from 'react';
import { useVolumeContext } from '../context/indexContext';

/**
 * Hook to automatically sync volume with audio/video elements
 * This hook will:
 * 1. Apply current volume to all existing audio/video elements
 * 2. Watch for new audio/video elements and apply volume to them
 * 3. Update volume when the context volume changes
 */
export function useVolumeSync() {
  const { outputVolume } = useVolumeContext();

  // Function to apply volume to all audio/video elements
  const applyVolumeToAllElements = useMemo(() => (volume) => {
    const normalizedVolume = volume / 100; // Convert 0-100 to 0-1.0

    const audioElements = document.querySelectorAll('audio');
    audioElements.forEach(audio => {
      audio.volume = normalizedVolume;
    });

    const videoElements = document.querySelectorAll('video');
    videoElements.forEach(video => {
      video.volume = normalizedVolume;
    });

    console.log(`Applied volume ${volume}% to ${audioElements.length} audio and ${videoElements.length} video elements`);
    return { audioCount: audioElements.length, videoCount: videoElements.length };
  }, []);

  // Apply volume whenever it changes
  useEffect(() => {
    applyVolumeToAllElements(outputVolume);
  }, [outputVolume, applyVolumeToAllElements]);

  // Watch for new audio/video elements and apply volume to them
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is an audio or video element
            if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
              node.volume = outputVolume / 100;
            }

            // Check for audio/video elements within the added node
            const audioElements = node.querySelectorAll && node.querySelectorAll('audio, video');
            if (audioElements) {
              audioElements.forEach((element) => {
                element.volume = outputVolume / 100;
              });
            }
          }
        });
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    return () => observer.disconnect();
  }, [outputVolume]);

  return { outputVolume, applyVolumeToAllElements };
}
