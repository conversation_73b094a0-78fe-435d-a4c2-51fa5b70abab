import { createContext, useContext, useState, useMemo, useRef } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)
  const isApplyingEffect = useRef(false) // Prevent multiple simultaneous applications

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
    isApplyingEffect,
  }), [currentEffect])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}

const NoiseSuppressionContext = createContext()
export function NoiseSuppressionProvider({ children }) {
  const [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled] = useState(false)

  const contextValue = useMemo(() => ({
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
  }), [isNoiseSuppressionEnabled])

  return (
    <NoiseSuppressionContext.Provider value={contextValue}>
      {children}
    </NoiseSuppressionContext.Provider>
  )
}

const VolumeContext = createContext()
export function VolumeProvider({ children }) {
  const [outputVolume, setOutputVolume] = useState(100)

  const onOutputVolumeChange = useMemo(() => (newVolume) => {
    setOutputVolume(newVolume)
  }, [])

  const contextValue = useMemo(() => ({
    outputVolume,
    onOutputVolumeChange,
  }), [outputVolume, onOutputVolumeChange])

  return (
    <VolumeContext.Provider value={contextValue}>
      {children}
    </VolumeContext.Provider>
  )
}

export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      <NoiseSuppressionProvider>
        <VolumeProvider>
          {children}
        </VolumeProvider>
      </NoiseSuppressionProvider>
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export function useNoiseSuppressionContext() {
  const context = useContext(NoiseSuppressionContext)
  if (!context) throw new Error('useNoiseSuppressionContext must be used within NoiseSuppressionProvider')
  return context
}

export function useVolumeContext() {
  const context = useContext(VolumeContext)
  if (!context) throw new Error('useVolumeContext must be used within VolumeProvider')
  return context
}



export default VirtualBackgroundContext