import { createContext, useContext, useState, useMemo, useRef, useEffect } from 'react'

const VirtualBackgroundContext = createContext()
export function VirtualBackgroundProvider({ children }) {
  const [currentEffect, setCurrentEffect] = useState(null)
  const isApplyingEffect = useRef(false) // Prevent multiple simultaneous applications

  const contextValue = useMemo(() => ({
    currentEffect,
    setCurrentEffect,
    isApplyingEffect,
  }), [currentEffect])

  return (
    <VirtualBackgroundContext.Provider value={contextValue}>
      {children}
    </VirtualBackgroundContext.Provider>
  )
}

const NoiseSuppressionContext = createContext()
export function NoiseSuppressionProvider({ children }) {
  const [isNoiseSuppressionEnabled, setIsNoiseSuppressionEnabled] = useState(false)

  const contextValue = useMemo(() => ({
    isNoiseSuppressionEnabled,
    setIsNoiseSuppressionEnabled,
  }), [isNoiseSuppressionEnabled])

  return (
    <NoiseSuppressionContext.Provider value={contextValue}>
      {children}
    </NoiseSuppressionContext.Provider>
  )
}

const VolumeContext = createContext()
export function VolumeProvider({ children }) {
  const [outputVolume, setOutputVolume] = useState(100)

  const onOutputVolumeChange = useMemo(() => (newVolume) => {
    setOutputVolume(newVolume)
  }, [])

  const contextValue = useMemo(() => ({
    outputVolume,
    onOutputVolumeChange,
  }), [outputVolume, onOutputVolumeChange])

  return (
    <VolumeContext.Provider value={contextValue}>
      {children}
    </VolumeContext.Provider>
  )
}

export function IndexProvider({ children }) {
  return (
    <VirtualBackgroundProvider>
      <NoiseSuppressionProvider>
        <VolumeProvider>
          {children}
        </VolumeProvider>
      </NoiseSuppressionProvider>
    </VirtualBackgroundProvider>
  )
}

export function useVirtualBackground() {
  const context = useContext(VirtualBackgroundContext)
  if (!context) throw new Error('useVirtualBackground must be used within VirtualBackgroundProvider')
  return context
}

export function useNoiseSuppressionContext() {
  const context = useContext(NoiseSuppressionContext)
  if (!context) throw new Error('useNoiseSuppressionContext must be used within NoiseSuppressionProvider')
  return context
}

export function useVolumeContext() {
  const context = useContext(VolumeContext)
  if (!context) throw new Error('useVolumeContext must be used within VolumeProvider')
  return context
}

// Hook to automatically sync volume with audio/video elements
export function useVolumeSync() {
  const { outputVolume } = useVolumeContext()

  // Function to apply volume to all audio/video elements
  const applyVolumeToAllElements = useMemo(() => (volume) => {
    const normalizedVolume = volume / 100 // Convert 0-100 to 0-1.0

    const audioElements = document.querySelectorAll('audio')
    audioElements.forEach(audio => {
      audio.volume = normalizedVolume
    })

    const videoElements = document.querySelectorAll('video')
    videoElements.forEach(video => {
      video.volume = normalizedVolume
    })

    console.log(`Applied volume ${volume}% to ${audioElements.length} audio and ${videoElements.length} video elements`)
    return { audioCount: audioElements.length, videoCount: videoElements.length }
  }, [])

  // Apply volume whenever it changes
  useEffect(() => {
    applyVolumeToAllElements(outputVolume)
  }, [outputVolume, applyVolumeToAllElements])

  // Watch for new audio/video elements and apply volume to them
  useEffect(() => {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if the added node is an audio or video element
            if (node.tagName === 'AUDIO' || node.tagName === 'VIDEO') {
              node.volume = outputVolume / 100
            }

            // Check for audio/video elements within the added node
            const audioElements = node.querySelectorAll && node.querySelectorAll('audio, video')
            if (audioElements) {
              audioElements.forEach((element) => {
                element.volume = outputVolume / 100
              })
            }
          }
        })
      })
    })

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })

    return () => observer.disconnect()
  }, [outputVolume])

  return { outputVolume, applyVolumeToAllElements }
}



export default VirtualBackgroundContext